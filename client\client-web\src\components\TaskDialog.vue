<template>
  <div>
    <el-dialog
      :before-close="handleClose"
      :visible.sync="dialogVisible"
      custom-class="task-dialog"
      title="新增仿真任务"
      width="800px"
    >
      <el-form ref="taskForm" :model="taskForm" :rules="rules" class="form-container" label-suffix=":" label-width="160px">
        <!-- 计算中心列表部分 -->
        <div class="section-header">
          <span class="section-title">计算中心列表</span>
          <el-button
            class="add-center-btn"
            size="small"
            @click="showAddComputingCenterDialog">
            <i class="el-icon-plus"></i> 添加计算中心
          </el-button>
        </div>

        <div class="section-content">
          <!-- 计算中心表格 -->
          <el-table
            v-if="computingCenters.length > 0"
            :data="computingCenters"
            :header-cell-style="{whiteSpace: 'nowrap', padding: '8px 12px'}"
            border
            class="center-table"
            style="width: 100%"
          >
            <el-table-column label="名称" min-width="180" prop="name" show-overflow-tooltip></el-table-column>
            <el-table-column label="算力规模(Pops/@FP16)" min-width="200" prop="pOPS" show-overflow-tooltip></el-table-column>
            <el-table-column label="节点数" min-width="80" prop="nNodes" show-overflow-tooltip></el-table-column>
            <el-table-column label="加速卡型号" min-width="120" prop="graphicsCardType" show-overflow-tooltip></el-table-column>
            <el-table-column label="加速卡数/节点" min-width="120" prop="nGraphicsCards" show-overflow-tooltip></el-table-column>
            <el-table-column label="显存/节点(GB)" min-width="130" prop="nGBMemoriesPerGraphicsCard" show-overflow-tooltip></el-table-column>
            <el-table-column label="CPUs/节点" min-width="100" prop="nCpus" show-overflow-tooltip></el-table-column>
            <el-table-column label="内存/节点(GB)" min-width="130" prop="nGBMemories" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" min-width="120">
              <template slot-scope="scope">
                <el-button type="text" @click="editComputingCenter(scope.row, scope.$index)">
                  <i class="el-icon-edit"></i> 编辑
                </el-button>
                <el-button type="text" @click="removeComputingCenter(scope.$index)">
                  <i class="el-icon-delete"></i> 删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="empty-table">
            <span>暂无计算中心，请点击"添加计算中心"按钮添加</span>
          </div>
        </div>

        <!-- 网络部分 -->
        <div class="section-header">
          <span class="section-title">网络</span>
        </div>

        <div class="section-content form-section">
          <div class="form-grid">
            <div class="form-item-container">
              <el-form-item label="构建网络拓扑" prop="construct">
                <el-switch v-model="taskForm.construct" active-color="#1289DD"></el-switch>
              </el-form-item>
            </div>

            <div class="form-item-container">
              <el-form-item label="网络传输加速" prop="accelerator">
                <el-switch v-model="taskForm.accelerator" active-color="#1289DD"></el-switch>
              </el-form-item>
            </div>

            <div class="form-item-container">
              <el-form-item label="网络拓扑选择" prop="topologyTpye">
                <div class="topology-select-container">
                  <el-select v-model="taskForm.topologyTpye" :disabled="!taskForm.construct" class="fixed-width" placeholder="请选择网络拓扑">
                    <el-option label="拓扑 01" value="topology01"></el-option>
                    <el-option label="拓扑 02" value="topology02"></el-option>
                    <el-option label="拓扑 03" value="topology03"></el-option>
                  </el-select>
                  <el-button
                    class="preview-topology-btn"
                    size="small"
                    type="primary"
                    @click="showTopologyPreview">
                    <i class="el-icon-picture"></i> 网络拓扑示意
                  </el-button>
                </div>
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 任务流配置部分 -->
        <div class="section-header">
          <span class="section-title">任务流配置（勾选）</span>
          <el-button
            class="preview-job-flow-btn"
            size="small"
            type="primary"
            @click="showJobFlowPreview">
            <i class="el-icon-data-analysis"></i> 任务流预览
          </el-button>
        </div>

        <div class="section-content form-section">
          <!-- 分布记录表格 -->
          <el-table
            v-if="computingCenters.length > 0"
            :data="distributionRecords"
            :header-cell-style="{whiteSpace: 'nowrap', padding: '8px 12px'}"
            border
            class="center-table"
            style="width: 100%"
            @selection-change="handleDistributionSelectionChange"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="计算中心" min-width="180" prop="centerName" show-overflow-tooltip></el-table-column>
            <el-table-column label="分布类型" min-width="120" prop="distributionType" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-tag :type="scope.row.distributionType === 'normal' ? 'primary' : 'success'">
                  {{ scope.row.distributionType === 'normal' ? '正态分布' : '泊松分布' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="任务数量(个)" min-width="100" prop="jobCount" show-overflow-tooltip></el-table-column>
            <el-table-column label="方差(个)" min-width="150" prop="var" show-overflow-tooltip></el-table-column>
            <el-table-column label="均值(个)" min-width="150" prop="mean" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" min-width="150">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  style="padding: 4px 8px; font-size: 12px;"
                  type="primary"
                  @click="previewSingleDistribution(scope.row)">
                  <i class="el-icon-data-analysis"></i> 预览
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="empty-table">
            <span>暂无计算中心，请先添加计算中心</span>
          </div>
        </div>

        <!-- 调度策略部分 -->
        <div class="section-header">
          <span class="section-title">调度策略</span>
        </div>

        <div class="section-content form-section">
          <div class="form-item-full form-row">
            <el-form-item label="调度策略" prop="scheduleType">
              <el-radio-group v-model="taskForm.scheduleType">
                <el-radio label="本地调度">本地调度</el-radio>
                <el-radio label="同构调度">同构调度</el-radio>
                <el-radio label="异构调度">异构调度</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <div class="form-grid">
            <div class="form-item-container">
              <el-form-item label="周期调度" prop="cycleSchedule">
                <el-switch v-model="taskForm.cycleSchedule" active-color="#1289DD"></el-switch>
              </el-form-item>
            </div>

            <div class="form-item-container">
              <el-form-item label="周期调度间隔（秒）" prop="timeResolution">
                <el-input
                  v-model.number="taskForm.timeResolution"
                  :max="10"
                  :min="0.1"
                  :step="0.1"
                  class="fixed-width"
                  type="number"
                  @keydown="validateNumberInput"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button class="preview-btn" type="info" @click="previewYaml">预览 YAML</el-button>
        <el-button class="cancel-btn" @click="handleClose">取 消</el-button>
        <el-button class="submit-btn" type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 添加计算中心的弹窗 -->
    <el-dialog
      :title="editIndex === -1 ? '添加计算中心' : '编辑计算中心'"
      :visible.sync="centerDialogVisible"
      append-to-body
      custom-class="task-dialog"
      width="600px"
    >
      <el-form ref="centerForm" :model="centerForm" :rules="centerRules" class="form-container" label-suffix=":" label-width="160px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="名称" prop="name">
              <el-select
                v-model="centerForm.name"
                class="fixed-width"
                filterable
                placeholder="请选择计算中心">
                <el-option
                  v-for="center in locationOptions"
                  :key="center.ID"
                  :label="center.Name"
                  :value="center.Name">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="算力规模" prop="pOPS">
              <el-input
                v-model.number="centerForm.pOPS"
                :max="2000"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
              <span class="unit-label">Pops/@FP16</span>
            </el-form-item>

            <el-form-item label="节点数" prop="nNodes">
              <el-input
                v-model.number="centerForm.nNodes"
                :max="1000"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
            </el-form-item>

            <el-form-item label="加速卡型号" prop="graphicsCardType">
              <el-select v-model="centerForm.graphicsCardType" class="fixed-width" placeholder="请选择加速卡型号">
                <el-option
                  v-for="type in graphicsCardTypes"
                  :key="type"
                  :label="type"
                  :value="type">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="加速卡数/节点" prop="nGraphicsCards">
              <el-input
                v-model.number="centerForm.nGraphicsCards"
                :max="8"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
              <span class="unit-label">个</span>
            </el-form-item>

            <el-form-item label="显存/节点" prop="nGBMemoriesPerGraphicsCard">
              <el-input
                v-model.number="centerForm.nGBMemoriesPerGraphicsCard"
                :max="100"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
              <span class="unit-label">GB</span>
            </el-form-item>

            <el-form-item label="CPUs/节点" prop="nCpus">
              <el-input
                v-model.number="centerForm.nCpus"
                :max="1024"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
            </el-form-item>

            <el-form-item label="内存/节点" prop="nGBMemories">
              <el-input
                v-model.number="centerForm.nGBMemories"
                :max="2000"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
              <span class="unit-label">GB</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="centerDialogVisible = false">取 消</el-button>
        <el-button class="submit-btn" type="primary" @click="submitCenterForm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- YAML预览对话框 -->
    <el-dialog
      :visible.sync="yamlPreviewVisible"
      append-to-body
      custom-class="yaml-preview-dialog"
      title="YAML 预览"
      width="800px"
    >
      <div v-if="yamlFilename" class="yaml-info">
        <span>文件名: {{ yamlFilename }}</span>
      </div>
      <div v-loading="yamlLoading" class="yaml-content-container">
        <pre v-if="yamlContent" class="yaml-content">{{ yamlContent }}</pre>
        <div v-else class="yaml-empty">
          <span>暂无内容，请点击"生成YAML"按钮生成预览</span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="yamlPreviewVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 网络拓扑预览对话框 -->
    <el-dialog
      :visible.sync="topologyPreviewVisible"
      append-to-body
      custom-class="topology-preview-dialog"
      title="网络拓扑示意"
      width="600px"
    >
      <div class="topology-preview-container">
        <el-image
          :preview-src-list="[currentTopologyImage]"
          :src="currentTopologyImage"
          fit="contain">
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline"></i>
          </div>
        </el-image>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="topologyPreviewVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 任务流预览对话框 -->
    <el-dialog
      :title="previewTitle"
      :visible.sync="jobFlowPreviewVisible"
      append-to-body
      custom-class="job-flow-preview-dialog"
      width="800px"
    >
      <div class="job-flow-preview-container">
        <div v-loading="jobFlowLoading" class="chart-container">
          <div ref="jobFlowChart" class="chart-wrapper"></div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="jobFlowPreviewVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { generateYaml } from '@/api/yamlService'
import dialogMixin from '@/mixins/dialogMixin'
import computingCenterMixin from '@/mixins/computingCenterMixin'
import yamlPreviewMixin from '@/mixins/yamlPreviewMixin'
import { getCenterFormRules, getDefaultTaskForm, getTaskFormRules, validateNumberInput } from '@/utils/form'
import { handleApiRequest } from '@/utils/apiHelper'
import { prepareTaskData } from '@/utils/yamlHelper'
import * as echarts from 'echarts'
import { getJobAnalysis } from '@/api/screenService'

export default {
  name: 'TaskDialog',
  mixins: [dialogMixin, computingCenterMixin, yamlPreviewMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    taskInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      taskForm: getDefaultTaskForm(),
      rules: {
        ...getTaskFormRules(),
        distributionRecords: [
          {
            validator: this.validateDistributionRecords,
            trigger: 'change'
          }
        ]
      },
      centerRules: getCenterFormRules(),
      topologyPreviewVisible: false,
      currentTopologyImage: '',
      jobFlowPreviewVisible: false,
      jobFlowLoading: false,
      jobFlowChart: null,
      distributionRecords: [], // 任务流配置数据
      selectedDistributions: [], // 选中的任务流配置
      currentDistribution: null, // 当前选中预览的配置
      previewTitle: '任务流预览', // 预览对话框标题

      // 加速卡型号选项（从默认配置中提取）
      graphicsCardTypes: ['ASCEND910', 'A100', 'V100', 'ENFLAME-T20']
    }
  },
  created () {
    // 不在created中自动获取数据，只在需要时获取
  },
  watch: {
    taskInfo: {
      handler (val) {
        if (val && Object.keys(val).length > 0) {
          this.taskForm = { ...getDefaultTaskForm(), ...val }
        }
      },
      deep: true
    },
    computingCenters: {
      handler (val) {
        this.updateDistributionRecords()
      },
      deep: true
    }
  },
  methods: {
    validateNumberInput,

    handleClose () {
      this.$refs.taskForm && this.$refs.taskForm.resetFields()
      this.dialogVisible = false
      this.computingCenters = [] // 清空计算中心列表
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    submitForm () {
      const apiRequestData = this.prepareTaskData()
      if (!apiRequestData) return

      handleApiRequest(() => generateYaml(apiRequestData), {
        loadingText: '正在提交任务...',
        successMessage: '任务提交成功！',
        errorMessage: '任务提交失败，请检查参数后重试',
        onSuccess: (response) => {
          // 延迟1秒后触发submit事件并关闭对话框
          setTimeout(() => {
            // 触发submit事件，response: response
            this.$emit('submit', response)
            this.handleClose() // 关闭对话框
          }, 1000)
        }
      })
    },

    showTopologyPreview () {
      if (this.taskForm.topologyTpye) {
        try {
          this.currentTopologyImage = require(`@/assets/topology/${this.taskForm.topologyTpye}.png`)
          this.topologyPreviewVisible = true
        } catch (error) {
          // 加载拓扑图片失败: error
          this.$message.error('拓扑图片加载失败')
        }
      } else {
        this.$message.warning('请先选择网络拓扑')
      }
    },

    showJobFlowPreview () {
      // 重置预览标题
      this.previewTitle = '任务流预览'

      if (!this.computingCenters || this.computingCenters.length === 0) {
        this.$message.warning('请先添加计算中心')
        return
      }

      // 检查是否有选中的任务流配置
      if (!this.selectedDistributions || this.selectedDistributions.length === 0) {
        this.$message.warning('请先选择任务流配置')
        return
      }

      // 选中的任务流配置: this.selectedDistributions

      this.jobFlowPreviewVisible = true
      this.jobFlowLoading = true

      // 构建新格式的参数，只包含被选中的配置
      const params = this.selectedDistributions.map(record => {
        // 从locationOptions中查找对应的ID
        const location = this.locationOptions.find(opt => opt.Name === record.centerName)
        const locationId = location ? location.ID : null

        if (!locationId) {
          // 未找到计算中心 ${record.centerName} 的ID
        }

        return {
          location_id: locationId || 0,
          distribution_type: record.distributionType // 使用记录中的分布类型
        }
      }).filter(item => item.location_id !== null)

      if (params.length === 0) {
        this.$message.warning('无法获取计算中心的ID')
        this.jobFlowLoading = false
        return
      }

      // 请求参数: params

      // 调用API获取数据
      getJobAnalysis(params)
        .then(response => {
          // API响应: response
          // 检查响应结构
          if (!response) {
            throw new Error('API返回为空')
          }

          // 尝试不同的响应结构
          const data = response.data || response
          // 处理后的数据: data

          if (!data || !Array.isArray(data)) {
            throw new Error('API返回数据格式错误，期望数组')
          }

          this.initJobFlowChart(data)
        })
        .catch(error => {
          // 获取任务流数据失败: error
          this.$message.error('获取任务流数据失败，请稍后重试')
        })
        .finally(() => {
          this.jobFlowLoading = false
        })
    },

    initJobFlowChart (data) {
      // 确保图表容器存在
      if (!this.$refs.jobFlowChart) {
        // 图表容器不存在
        return
      }

      // 检查数据是否有效
      if (!Array.isArray(data)) {
        // 数据格式错误，期望数组: data
        this.$message.error('数据格式错误，无法显示图表')
        return
      }

      // 销毁旧图表实例
      if (this.jobFlowChart) {
        this.jobFlowChart.dispose()
      }

      // 创建新图表实例
      this.jobFlowChart = echarts.init(this.$refs.jobFlowChart)

      // 处理数据 - 新格式不再区分类型
      const hours = [...new Set(data.map(item => item.hour_label))].sort((a, b) => a - b)
      const jobData = []

      hours.forEach(hour => {
        const item = data.find(item => item.hour_label === hour)
        jobData.push(item ? item.job_count : 0)
      })

      // 处理后的数据: hours, jobData

      // 配置图表选项
      const option = {
        backgroundColor: '#001529',
        title: {
          text: '任务流分布',
          left: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 21, 41, 0.9)',
          borderColor: '#234157',
          borderWidth: 1,
          textStyle: {
            color: '#fff'
          },
          formatter: function (params) {
            let result = `<div style="font-weight: bold; margin-bottom: 5px; color: #fff;">${params[0].axisValue}</div>`
            params.forEach(param => {
              result += `<div style="display: flex; justify-content: space-between; margin: 2px 0;">
                <span style="color: ${param.color}">${param.seriesName}</span>
                <span style="margin-left: 20px; color: #fff">${param.value}</span>
              </div>`
            })
            return result
          }
        },
        legend: {
          data: ['任务数量'],
          top: 30,
          textStyle: {
            color: '#fff'
          }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: hours.map(hour => `${hour}H`),
          name: '时间',
          nameTextStyle: {
            color: '#fff'
          },
          axisLine: {
            lineStyle: {
              color: '#234157'
            }
          },
          axisLabel: {
            color: '#fff'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(35, 65, 87, 0.3)',
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '任务数量',
          nameTextStyle: {
            color: '#fff'
          },
          axisLine: {
            lineStyle: {
              color: '#234157'
            }
          },
          axisLabel: {
            color: '#fff'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(35, 65, 87, 0.3)',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '任务数量',
            type: 'line',
            data: jobData,
            smooth: true,
            symbol: 'none',
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              width: 2
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.2)'
                }, {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.05)'
                }]
              }
            }
          }
        ]
      }

      // 设置图表配置
      this.jobFlowChart.setOption(option)

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleJobFlowChartResize)
    },

    handleJobFlowChartResize () {
      if (this.jobFlowChart) {
        this.jobFlowChart.resize()
      }
    },

    beforeDestroy () {
      // 移除事件监听
      window.removeEventListener('resize', this.handleJobFlowChartResize)
      // 销毁图表实例
      if (this.jobFlowChart) {
        this.jobFlowChart.dispose()
      }
    },

    // 更新分布记录数据
    updateDistributionRecords () {
      this.distributionRecords = []
      this.computingCenters.forEach(center => {
        // 查找对应的location数据
        const location = this.locationOptions.find(opt => opt.Name === center.name) || {}
        // 添加正态分布记录
        this.distributionRecords.push({
          centerName: center.name,
          distributionType: 'normal', // 使用英文值
          jobCount: location.NormalCount,
          mean: location.NormalMean,
          var: location.NormalVar
        })

        // 添加泊松分布记录
        this.distributionRecords.push({
          centerName: center.name,
          distributionType: 'poisson', // 使用英文值
          jobCount: location.PoissonCount,
          mean: location.PoissonMean,
          var: location.PoissonVar
        })
      })
    },

    // 处理任务流配置选择变化
    handleDistributionSelectionChange (selection) {
      this.selectedDistributions = selection

      if (selection && selection.length > 0) {
        this.taskForm.jsonFileList = selection.map(record =>
          `/data/TaskFileList/${record.centerName}-${record.distributionType}.json`
        )
      } else {
        this.taskForm.jsonFileList = []
      }
    },

    // 扩展prepareTaskData方法以包含选中的任务流配置
    prepareTaskData () {
      // 验证是否选择了任务流配置
      if (!this.selectedDistributions || this.selectedDistributions.length === 0) {
        this.$message.warning('请至少选择一个任务流配置')
        return null
      }

      // 设置useJsonFile为true
      this.taskForm.useJsonFile = true

      const baseData = prepareTaskData(this.taskForm, this.computingCenters)
      if (!baseData) return null

      // 添加选中的任务流配置与jsonFileList
      baseData.distributionRecords = this.selectedDistributions
      return baseData
    },

    // 添加任务流配置验证规则
    validateDistributionRecords (rule, value, callback) {
      if (!this.selectedDistributions || this.selectedDistributions.length === 0) {
        callback(new Error('请至少选择一个任务流配置'))
      } else {
        callback()
      }
    },

    // 预览单个任务流配置
    previewSingleDistribution (distribution) {
      if (!distribution) {
        this.$message.warning('配置信息不完整')
        return
      }

      // 保存当前选中的配置
      this.currentDistribution = distribution

      // 设置预览标题
      const distType = distribution.distributionType === 'normal' ? '正态分布' : '泊松分布'
      this.previewTitle = `任务流预览 - ${distribution.centerName} (${distType})`

      // 预览单个任务流配置: distribution

      this.jobFlowPreviewVisible = true
      this.jobFlowLoading = true

      // 构建参数
      const location = this.locationOptions.find(opt => opt.Name === distribution.centerName)
      const locationId = location ? location.ID : null

      if (!locationId) {
        this.$message.warning(`未找到计算中心 ${distribution.centerName} 的ID`)
        this.jobFlowLoading = false
        return
      }

      const params = [{
        location_id: locationId,
        distribution_type: distribution.distributionType
      }]

      // 请求参数: params

      // 调用API获取数据
      getJobAnalysis(params)
        .then(response => {
          // API响应: response
          // 检查响应结构
          if (!response) {
            throw new Error('API返回为空')
          }

          // 尝试不同的响应结构
          const data = response.data || response
          // 处理后的数据: data

          if (!data || !Array.isArray(data)) {
            throw new Error('API返回数据格式错误，期望数组')
          }

          this.initJobFlowChart(data)
        })
        .catch(error => {
          // 获取任务流数据失败: error
          this.$message.error('获取任务流数据失败，请稍后重试')
        })
        .finally(() => {
          this.jobFlowLoading = false
        })
    },

    // 处理表格行点击
    handleRowClick (row, column, event) {
      // 如果点击的是多选框列，不触发预览
      if (column.type === 'selection') {
        return
      }

      // 调用预览功能
      this.previewSingleDistribution(row)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/dialog.scss';

/* 特定于TaskDialog的额外样式 */
.topology-select-container {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;

  .el-select {
    flex: 1;
  }

  .preview-topology-btn {
    flex-shrink: 0;
    white-space: nowrap;
  }
}

.topology-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;

  .el-image {
    max-width: 100%;
    max-height: 400px;
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 30px;
  }
}

.json-select-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.job-flow-preview-container {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  background-color: #001529;

  .chart-container {
    width: 100%;
    height: 400px;
    background-color: #001529;
    border-radius: 4px;
    padding: 0;
    margin: 0;
    position: relative;
    overflow: hidden;
  }

  .chart-wrapper {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }
}

/* 任务流预览按钮样式 */
.preview-job-flow-btn {
  margin-left: 10px;
  padding: 7px 5px;
  min-width: auto;
  font-size: 12px;
}
</style>
