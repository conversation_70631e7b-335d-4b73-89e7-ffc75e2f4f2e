<template>
  <div class="computing-center-manager">
    <!-- 计算中心列表部分 -->
    <div class="section-header">
      <span class="section-title">计算中心列表</span>
      <el-button
        class="add-center-btn"
        size="small"
        @click="showAddComputingCenterDialog">
        <i class="el-icon-plus"></i> 添加计算中心
      </el-button>
    </div>

    <div class="section-content">
      <!-- 计算中心表格 -->
      <el-table
        v-if="computingCenters.length > 0"
        :data="computingCenters"
        :header-cell-style="{whiteSpace: 'nowrap', padding: '8px 12px'}"
        border
        class="center-table"
        style="width: 100%"
      >
        <el-table-column label="名称" min-width="180" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="算力规模(Pops/@FP16)" min-width="200" prop="pOPS" show-overflow-tooltip></el-table-column>
        <el-table-column label="节点数" min-width="80" prop="nNodes" show-overflow-tooltip></el-table-column>
        <el-table-column label="加速卡型号" min-width="120" prop="graphicsCardType" show-overflow-tooltip></el-table-column>
        <el-table-column label="加速卡数/节点" min-width="120" prop="nGraphicsCards" show-overflow-tooltip></el-table-column>
        <el-table-column label="显存/节点(GB)" min-width="130" prop="nGBMemoriesPerGraphicsCard" show-overflow-tooltip></el-table-column>
        <el-table-column label="CPUs/节点" min-width="100" prop="nCpus" show-overflow-tooltip></el-table-column>
        <el-table-column label="内存/节点(GB)" min-width="130" prop="nGBMemories" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" min-width="120">
          <template slot-scope="scope">
            <el-button type="text" @click="editComputingCenter(scope.row, scope.$index)">
              <i class="el-icon-edit"></i> 编辑
            </el-button>
            <el-button type="text" @click="removeComputingCenter(scope.$index)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-else class="empty-table">
        <span>{{ emptyText }}</span>
      </div>
    </div>

    <!-- 添加计算中心的弹窗 -->
    <el-dialog
      :title="editIndex === -1 ? '添加计算中心' : '编辑计算中心'"
      :visible.sync="centerDialogVisible"
      :close-on-click-modal="false"
      append-to-body
      custom-class="task-dialog"
      width="600px"
    >
      <el-form ref="centerForm" :model="centerForm" :rules="centerRules" class="form-container" label-suffix=":" label-width="160px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="名称" prop="name">
              <el-select
                v-model="centerForm.name"
                class="fixed-width"
                filterable
                placeholder="请选择计算中心">
                <el-option
                  v-for="center in locationOptions"
                  :key="center.ID"
                  :label="center.Name"
                  :value="center.Name">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="算力规模" prop="pOPS">
              <el-input
                v-model.number="centerForm.pOPS"
                :max="2000"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
              <span class="unit-label">Pops/@FP16</span>
            </el-form-item>

            <el-form-item label="节点数" prop="nNodes">
              <el-input
                v-model.number="centerForm.nNodes"
                :max="1000"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
            </el-form-item>

            <el-form-item label="加速卡型号" prop="graphicsCardType">
              <el-select v-model="centerForm.graphicsCardType" class="fixed-width" placeholder="请选择加速卡型号">
                <el-option
                  v-for="type in graphicsCardTypes"
                  :key="type"
                  :label="type"
                  :value="type">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="加速卡数/节点" prop="nGraphicsCards">
              <el-input
                v-model.number="centerForm.nGraphicsCards"
                :max="8"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
              <span class="unit-label">个</span>
            </el-form-item>

            <el-form-item label="显存/节点" prop="nGBMemoriesPerGraphicsCard">
              <el-input
                v-model.number="centerForm.nGBMemoriesPerGraphicsCard"
                :max="100"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
              <span class="unit-label">GB</span>
            </el-form-item>

            <el-form-item label="CPUs/节点" prop="nCpus">
              <el-input
                v-model.number="centerForm.nCpus"
                :max="1024"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
            </el-form-item>

            <el-form-item label="内存/节点" prop="nGBMemories">
              <el-input
                v-model.number="centerForm.nGBMemories"
                :max="2000"
                :min="1"
                class="fixed-width"
                type="number"
                @keydown="validateNumberInput"
              ></el-input>
              <span class="unit-label">GB</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="centerDialogVisible = false">取 消</el-button>
        <el-button class="submit-btn" type="primary" @click="submitCenterForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import computingCenterMixin from '@/mixins/computingCenterMixin'
import dialogMixin from '@/mixins/dialogMixin'
import { validateNumberInput, getCenterFormRules } from '@/utils/form'

export default {
  name: 'ComputingCenterManager',
  mixins: [computingCenterMixin, dialogMixin],
  props: {
    value: {
      type: Array,
      default: () => []
    },
    emptyText: {
      type: String,
      default: '暂无计算中心，请点击"添加计算中心"按钮添加'
    },
    // 是否显示某些列（预留扩展）
    visibleColumns: {
      type: Array,
      default: () => ['name', 'pOPS', 'nNodes', 'graphicsCardType', 'nGraphicsCards', 'nGBMemoriesPerGraphicsCard', 'nCpus', 'nGBMemories']
    }
  },
  data () {
    return {
      // 加速卡型号选项
      graphicsCardTypes: ['ASCEND910', 'A100', 'V100', 'ENFLAME-T20'],
      // 表单验证规则
      centerRules: getCenterFormRules()
    }
  },
  created () {
    // 初始化时获取位置选项
    this.fetchLocationOptions()
    // 初始化计算中心数据（使用 mixin 中的 computingCenters）
    this.computingCenters = [...this.value]
  },
  watch: {
    value: {
      handler (newVal) {
        // 避免循环更新，只有当值真正不同时才更新
        if (JSON.stringify(newVal) !== JSON.stringify(this.computingCenters)) {
          this.computingCenters = [...newVal]
        }
      },
      deep: true
    },
    computingCenters: {
      handler (newVal) {
        // 避免循环更新，只有当值真正不同时才emit
        if (JSON.stringify(newVal) !== JSON.stringify(this.value)) {
          this.$emit('input', newVal)
          this.$emit('change', newVal)
        }
      },
      deep: true
    }
  },
  methods: {
    validateNumberInput
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/dialog.scss';

.computing-center-manager {
  .section-header {
    margin: 5px 0;
    border-bottom: 1px solid rgba(18, 137, 221, 0.5);
    padding-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-title {
    color: #32c5ff;
    font-size: 16px;
    font-weight: bold;
    padding-left: 8px;
    border-left: 3px solid #32c5ff;
  }

  .section-content {
    padding: 5px 0 15px 0;
  }

  .empty-table {
    text-align: center;
    padding: 40px 0;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
  }

  .add-center-btn {
    background: rgba(18, 137, 221, 0.2);
    border: 1px solid rgba(18, 137, 221, 0.6);
    color: #32c5ff;
    padding: 5px 12px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(18, 137, 221, 0.3);
      border-color: #32c5ff;
      color: #fff;
      box-shadow: 0 0 8px rgba(50, 197, 255, 0.3);
    }
  }

  .unit-label {
    margin-left: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
  }
}
</style>
